import { IsNotEmpty, <PERSON>PhoneNumber, IsStrongPassword, Length } from "class-validator";

export class RegisterDto {
  @IsNotEmpty({ message: '用户名不能为空' })
  username: string;
  @IsNotEmpty({ message: '手机号不能为空' })
  @IsPhoneNumber('CN', { message: '手机号格式不正确' })
  phoneNumber: string;
  @IsNotEmpty({ message: '验证码不能为空' })
  @Length(6, 6, { message: '验证码格式不正确' })
  code: string; // 验证码
  @IsNotEmpty({ message: '密码不能为空' })
  @IsStrongPassword({ minLength: 6, message: '密码强度不足' })
  password: string;
  @IsNotEmpty({ message: '确认密码不能为空' })
  confirmPassword: string;
}
